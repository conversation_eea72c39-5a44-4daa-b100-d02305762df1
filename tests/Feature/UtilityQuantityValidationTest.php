<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class UtilityQuantityValidationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Field $field;
    private Utility $utility;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8,
        ]);
        $this->utility = Utility::factory()->create([
            'name' => 'Test Utility',
            'hourly_rate' => 10.00,
            'is_active' => true,
        ]);
    }

    #[Test]
    public function utility_quantities_must_be_whole_numbers()
    {
        $this->actingAs($this->user);

        // Test that decimal utility quantities are rejected
        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 1.5, // Field duration can be decimal
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $this->utility->id,
                    'hours' => 1.5, // This should be rejected
                ]
            ],
        ]);

        $response->assertSessionHasErrors('utilities.0.hours');
    }

    #[Test]
    public function utility_quantities_accept_whole_numbers()
    {
        $this->actingAs($this->user);

        // Test that whole number utility quantities are accepted
        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 1.5, // Field duration can be decimal
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $this->utility->id,
                    'hours' => 2, // This should be accepted
                ]
            ],
        ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }

    #[Test]
    public function utility_quantities_reject_zero_and_negative()
    {
        $this->actingAs($this->user);

        // Test zero quantity
        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 1.5,
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $this->utility->id,
                    'hours' => 0, // This should be rejected
                ]
            ],
        ]);

        $response->assertSessionHasErrors('utilities.0.hours');
    }

    #[Test]
    public function field_duration_still_accepts_half_hour_increments()
    {
        $this->actingAs($this->user);

        // Test that field duration can still be decimal
        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2.5, // Field duration should accept decimals
            'customer_name' => 'Test Customer',
        ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }
}
