<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update bookings table to support decimal duration_hours
        Schema::table('bookings', function (Blueprint $table) {
            // Change duration_hours from integer to decimal(4,1)
            $table->decimal('duration_hours', 4, 1)->change();
        });

        // Update fields table to support decimal min/max booking hours
        Schema::table('fields', function (Blueprint $table) {
            // Change min_booking_hours and max_booking_hours from integer to decimal(4,1)
            $table->decimal('min_booking_hours', 4, 1)->change();
            $table->decimal('max_booking_hours', 4, 1)->change();
        });

        // Update existing data to ensure compatibility
        // Convert any existing integer values to decimal format
        DB::statement('UPDATE bookings SET duration_hours = CAST(duration_hours AS DECIMAL(4,1))');
        DB::statement('UPDATE fields SET min_booking_hours = CAST(min_booking_hours AS DECIMAL(4,1))');
        DB::statement('UPDATE fields SET max_booking_hours = CAST(max_booking_hours AS DECIMAL(4,1))');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert bookings table back to integer duration_hours
        Schema::table('bookings', function (Blueprint $table) {
            // Convert back to integer (this will truncate decimal values)
            $table->integer('duration_hours')->change();
        });

        // Revert fields table back to integer min/max booking hours
        Schema::table('fields', function (Blueprint $table) {
            // Convert back to integer (this will truncate decimal values)
            $table->integer('min_booking_hours')->change();
            $table->integer('max_booking_hours')->change();
        });
    }
};
